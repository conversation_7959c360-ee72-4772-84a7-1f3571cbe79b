import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/advance.dart';
import '../../services/advance_service.dart';
import 'add_advance_screen.dart';
import 'advance_details_screen.dart';
import 'add_payment_screen.dart';

class AdvancesScreen extends StatefulWidget {
  const AdvancesScreen({super.key});

  @override
  State<AdvancesScreen> createState() => _AdvancesScreenState();
}

class _AdvancesScreenState extends State<AdvancesScreen>
    with SingleTickerProviderStateMixin {
  final AdvanceService _advanceService = AdvanceService();
  late TabController _tabController;

  List<Advance> _allAdvances = [];
  List<Advance> _outstandingAdvances = [];
  List<Advance> _paidAdvances = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAdvances();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAdvances() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final advances = await _advanceService.getAllAdvances();
      final outstanding = await _advanceService.getOutstandingAdvances();

      setState(() {
        _allAdvances = advances;
        _outstandingAdvances = outstanding;
        _paidAdvances = advances
            .where((advance) => advance.status == AdvanceStatus.fullyPaid)
            .toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل السلف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة السلف'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAdvances,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الكل'),
            Tab(text: 'المستحقة'),
            Tab(text: 'المسددة'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildAdvancesList(_allAdvances, 'لا توجد سلف'),
                _buildAdvancesList(_outstandingAdvances, 'لا توجد سلف مستحقة'),
                _buildAdvancesList(_paidAdvances, 'لا توجد سلف مسددة'),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddAdvanceScreen(),
            ),
          );
          if (result == true) {
            _loadAdvances();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAdvancesList(List<Advance> advances, String emptyMessage) {
    if (advances.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على زر + لإضافة سلفة جديدة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAdvances,
      child: ListView.builder(
        itemCount: advances.length,
        itemBuilder: (context, index) {
          final advance = advances[index];
          return _buildAdvanceCard(advance);
        },
      ),
    );
  }

  Widget _buildAdvanceCard(Advance advance) {
    Color statusColor;
    IconData statusIcon;

    switch (advance.status) {
      case AdvanceStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case AdvanceStatus.partiallyPaid:
        statusColor = Colors.blue;
        statusIcon = Icons.payments;
        break;
      case AdvanceStatus.fullyPaid:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: statusColor,
          child: Icon(
            statusIcon,
            color: Colors.white,
          ),
        ),
        title: Text(
          advance.employee?.name ?? 'موظف غير معروف',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(advance.reason),
            Text(
              'تاريخ الاستلام: ${DateFormat('dd/MM/yyyy').format(advance.receivedDate)}',
              style: TextStyle(color: Colors.grey[600]),
            ),
            Text(
              advance.status.arabicName,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س',
                decimalDigits: 2,
              ).format(advance.amount),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            if (advance.remainingAmount > 0)
              Text(
                'متبقي: ${NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'ر.س',
                  decimalDigits: 2,
                ).format(advance.remainingAmount)}',
                style: TextStyle(
                  color: Colors.red[600],
                  fontSize: 12,
                ),
              ),
          ],
        ),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AdvanceDetailsScreen(advance: advance),
            ),
          );
          if (result == true) {
            _loadAdvances();
          }
        },
        onLongPress: () => _showAdvanceOptions(advance),
      ),
    );
  }

  void _showAdvanceOptions(Advance advance) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.visibility),
            title: const Text('عرض التفاصيل'),
            onTap: () async {
              Navigator.pop(context);
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AdvanceDetailsScreen(advance: advance),
                ),
              );
              if (result == true) {
                _loadAdvances();
              }
            },
          ),
          if (advance.status != AdvanceStatus.fullyPaid)
            ListTile(
              leading: const Icon(Icons.payment),
              title: const Text('إضافة دفعة'),
              onTap: () async {
                Navigator.pop(context);
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AddPaymentScreen(advance: advance),
                  ),
                );
                if (result == true) {
                  _loadAdvances();
                }
              },
            ),
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('تعديل'),
            onTap: () async {
              Navigator.pop(context);
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AddAdvanceScreen(advance: advance),
                ),
              );
              if (result == true) {
                _loadAdvances();
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('حذف', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context);
              _deleteAdvance(advance);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAdvance(Advance advance) async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف سلفة "${advance.employee?.name ?? 'موظف غير معروف'}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _advanceService.deleteAdvance(advance.id!);
        _loadAdvances();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف السلفة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف السلفة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
